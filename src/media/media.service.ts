import {
    BadRequestException,
    ForbiddenException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { CompanyService } from "src/company/company.service";
import { FormDocument } from "src/opportunity/schema/form.schema";
import { ShareMediaDocument } from "src/media/schema/share-media.schema";
import { PositionService } from "src/position/position.service";
import { RoleDocument } from "src/role/schema/role.schema";
import { S3Service } from "src/s3/s3.service";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { deflateSync, inflateSync } from "zlib";
import { CreateMediaDto } from "./dto/create-media.dto";
import { GetMediaQueryDto } from "./dto/get-media.dto";
import { ShareMediaQueryDto } from "./dto/share-media.dto";
import { UpdateMediaDto } from "./dto/update-media.dto";
import { MediaDocument } from "./schema/media.schema";

@Injectable()
export class MediaService {
    constructor(
        private readonly companyService: CompanyService,
        private readonly s3Service: S3Service,
        private readonly positionService: PositionService,
        @InjectModel("ShareMedia") private shareMediaModel: Model<ShareMediaDocument>,
        @InjectModel("Role") private readonly roleModel: Model<RoleDocument>,
        @InjectModel("Forms") private readonly formsModel: Model<FormDocument>,
        @InjectModel("Media") private readonly mediaModel: Model<MediaDocument>,
    ) {}

    async createMedia(companyId: string, memberId: string, createMediaDto: CreateMediaDto) {
        try {
            const { oppId, images } = createMediaDto;

            if (!images?.length) {
                throw new BadRequestException("No images provided.");
            }

            // Pre-fetch form counts to avoid multiple DB queries
            const formCounts: Record<string, number> = {};
            const formIds = images.map((img) => img.builderFormId).filter(Boolean);

            if (formIds.length) {
                const counts = await this.mediaModel.aggregate([
                    { $match: { builderFormId: { $in: formIds } } },
                    { $group: { _id: "$builderFormId", count: { $sum: 1 } } },
                ]);
                counts.forEach(({ _id, count }) => {
                    formCounts[_id] = count;
                });
            }

            const tagsSet = new Set<string>(); // Collect unique tags
            const createdMedia = []; // Store created media details for response

            // Store each image as a separate document
            await Promise.all(
                images.map(async (imageData) => {
                    const { builderFormId, name, tags = [], ...rest } = imageData;

                    const newMedia = new this.mediaModel({
                        companyId,
                        oppId,
                        name: builderFormId ? `${name} (${(formCounts[builderFormId] || 0) + 1})` : name,
                        tags,
                        builderFormId,
                        createdBy: memberId,
                        ...rest,
                    });

                    const savedMedia = await newMedia.save();

                    // Collect tags
                    if (tags?.length) {
                        tags.forEach((tag) => tagsSet.add(tag));
                    }

                    // Push created media details for response
                    createdMedia.push({
                        _id: savedMedia._id,
                        mimetype: savedMedia.mimetype,
                        url: savedMedia.url,
                        tags: savedMedia.tags || [],
                        formFieldByName: savedMedia.formFieldByName,
                    });
                }),
            );

            // Add collected tags to the company’s tag list
            if (tagsSet.size) {
                this.companyService.addTagsToList(companyId, Array.from(tagsSet));
            }

            return new CreatedResponse({
                message: "Opportunity Media created successfully!",
                images: createdMedia, // Include media details in response
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(
                error.message || "An error occurred while creating Opportunity Media",
            );
        }
    }

    async updateMedia(companyId: string, updateMediaDto: UpdateMediaDto) {
        try {
            const { images } = updateMediaDto;

            if (!images || images.length === 0) {
                throw new BadRequestException("No images provided for update.");
            }

            const bulkOps = images.map((image) => ({
                updateOne: {
                    filter: { _id: image._id, companyId }, // Ensures only media from this company is updated
                    update: { $set: image },
                },
            }));

            // Execute bulk update
            const bulkResult = await this.mediaModel.bulkWrite(bulkOps);

            if (bulkResult.modifiedCount === 0) {
                throw new HttpException(
                    "No media was updated. Check the provided IDs.",
                    HttpStatus.NOT_MODIFIED,
                );
            }

            // Extract unique tags and update company tag list
            const uniqueTags = [
                ...new Set(images.flatMap((img) => img.tags || [])), // Flatten and remove duplicates
            ];

            if (uniqueTags.length > 0) {
                this.companyService.addTagsToList(companyId, uniqueTags);
            }

            return new OkResponse({
                message: `${bulkResult.modifiedCount} media record(s) updated successfully.`,
            });
        } catch (error) {
            throw new InternalServerErrorException(error.message || "Error updating Opportunity Media");
        }
    }

    async deleteImagesFromMedia(companyId: string, imageIds: string[], user: JwtUserPayload) {
        try {
            // Fetch user role for permissions check
            const userRole = await this.roleModel.findOne({ memberId: user.memberId });

            // Find images to delete
            const imagesToDelete = await this.mediaModel.find({
                _id: { $in: imageIds },
                companyId: companyId,
            });

            if (!imagesToDelete.length) {
                throw new NotFoundException("No matching images found.");
            }

            // Permission check
            if (user.teamPermission === PermissionsEnum.None) {
                throw new ForbiddenException("Insufficient permissions.");
            }

            let allowedToDelete = false;
            if (userRole?.role === 1) {
                allowedToDelete = true; // Admin can delete any image
            } else {
                allowedToDelete = imagesToDelete.every((image) => image.createdBy === user.memberId);
            }

            if (!allowedToDelete) {
                throw new ForbiddenException("You do not have permission to delete these images.");
            }

            // Collect image URLs for S3 deletion
            const imageUrls = imagesToDelete.flatMap((image) => image.url).filter(Boolean);

            // Delete images from S3
            if (imageUrls.length) {
                await this.s3Service.deleteMultipleImageS3(imageUrls);
            }

            // Delete images from the database
            await this.mediaModel.deleteMany({ _id: { $in: imageIds } });

            // Delete associated form records only for pdf
            const formIdsToDelete = imagesToDelete.map((image) => image.formId).filter(Boolean);

            if (formIdsToDelete.length) {
                await this.formsModel.deleteMany({ _id: { $in: formIdsToDelete } });
            }

            return new OkResponse({
                message: "Images deleted successfully",
                deletedImageIds: imageIds,
            });
        } catch (error) {
            if (error instanceof ForbiddenException || error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException("Error while deleting images, please try again.");
        }
    }

    async getMedia(companyId: string, oppId: string, getMediaQueryDto?: GetMediaQueryDto) {
        try {
            const { imageIds, tags, createdBy, types } = getMediaQueryDto || {};

            const query = {
                companyId,
                oppId,
                ...(imageIds?.length && { _id: { $in: imageIds } }),
                ...(tags?.length && { tags: { $in: tags } }),
                ...(createdBy?.length && { createdBy: { $in: createdBy } }),
                ...(types?.length && { mimetype: { $in: types } }),
            };

            const result = await this.mediaModel.aggregate([
                {
                    $match: query,
                },
                // Lookup opportunity data for the first document
                {
                    $lookup: {
                        from: "Opportunity",
                        localField: "oppId",
                        foreignField: "_id",
                        as: "opportunityData",
                        pipeline: [{ $project: { PO: 1, num: 1, contractId: 1 } }],
                    },
                },
                { $unwind: { path: "$opportunityData", preserveNullAndEmptyArrays: true } },
                // Lookup client data for the first document
                {
                    $lookup: {
                        from: "Contact", // Changed from Client
                        localField: "opportunityData.contractId",
                        foreignField: "_id",
                        as: "clientData",
                        pipeline: [{ $project: { fullName: 1, businessName: 1 } }], // Updated fields
                    },
                },
                { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: true } },
                // Lookup member and user data for all documents
                {
                    $lookup: {
                        from: "Member",
                        localField: "createdBy",
                        foreignField: "_id",
                        as: "memberData",
                        pipeline: [{ $project: { _id: 1, name: 1, user: 1 } }],
                    },
                },
                { $unwind: { path: "$memberData", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: "User",
                        localField: "memberData.user",
                        foreignField: "_id",
                        as: "userData",
                        pipeline: [{ $project: { _id: 1, imageUrl: 1 } }],
                    },
                },
                { $unwind: { path: "$userData", preserveNullAndEmptyArrays: true } },
                // Add fields for createdBy
                {
                    $addFields: {
                        createdBy: {
                            _id: "$memberData._id",
                            name: { $ifNull: ["$memberData.name", "Unknown"] },
                            userImageUrl: {
                                _id: "$userData._id",
                                imageUrl: "$userData.imageUrl",
                            },
                        },
                    },
                },
                // Group to separate the first document (for opportunity info) and the rest (for media)
                {
                    $group: {
                        _id: null,
                        allDocuments: { $push: "$$ROOT" },
                        opportunityInfo: {
                            $first: {
                                PO: "$opportunityData.PO",
                                num: "$opportunityData.num",
                                firstName: "$clientData.firstName",
                                lastName: "$clientData.lastName",
                                _id: "$_id",
                            },
                        },
                    },
                },
                // Project the final structure
                {
                    $project: {
                        opportunity: "$opportunityInfo",
                        images: {
                            $map: {
                                input: "$allDocuments",
                                as: "doc",
                                in: {
                                    _id: "$$doc._id",
                                    name: "$$doc.name",
                                    mimetype: "$$doc.mimetype",
                                    url: "$$doc.url",
                                    thumbnail: "$$doc.thumbnail",
                                    tags: "$$doc.tags",
                                    location: "$$doc.location",
                                    createdAt: "$$doc.createdAt",
                                    createdBy: "$$doc.createdBy",
                                    builderFormId: "$$doc.builderFormId",
                                    formId: "$$doc.formId",
                                },
                            },
                        },
                    },
                },
            ]);

            // If no documents found, return empty response
            if (!result.length || !result[0].opportunity.PO) {
                return new OkResponse({ opportunity: null, images: [] });
            }

            return new OkResponse({
                PO: result[0].opportunity.PO,
                num: result[0].opportunity.num,
                firstName: result[0].opportunity.firstName,
                lastName: result[0].opportunity.lastName,
                oppId: result[0].opportunity._id,
                images: result[0].images,
            });
        } catch (error) {
            console.error("Error fetching opportunity media:", error);
            throw new Error("Failed to fetch opportunity media.");
        }
    }

    async getAllUploadedMedia(
        companyId: string,
        memberId: string,
        permission: PermissionsEnum,
        getQueryDto?: GetMediaQueryDto,
    ) {
        try {
            const { imageIds, tags, types, createdBy, limit = 10 } = getQueryDto;
            // Calculate skip value for pagination
            const skip = ((getQueryDto?.skip || 1) - 1) * limit;
            const filterConditions: any = { companyId };

            if (permission !== PermissionsEnum.Full || createdBy?.length) {
                // Get list of members managed by logged-in member
                const { members } = await this.positionService.getManagedMembersInternal(
                    memberId,
                    companyId,
                    permission,
                );
                filterConditions.createdBy = createdBy?.length ? { $in: createdBy } : { $in: members };
            }

            // Add filters based on imageIds, types, and tags
            if (imageIds?.length) filterConditions._id = { $in: imageIds };
            if (types?.length) {
                filterConditions.mimetype = { $in: types.map((type) => new RegExp(`^${type}/`, "i")) };
            }
            if (tags?.length) {
                filterConditions.tags = { $in: tags };
            }

            const query: any = [
                { $match: filterConditions },

                // Lookup Opportunity Data
                {
                    $lookup: {
                        from: "Opportunity",
                        localField: "oppId",
                        foreignField: "_id",
                        as: "opportunityData",
                        pipeline: [{ $project: { PO: 1, num: 1, contractId: 1 } }],
                    },
                },
                { $unwind: { path: "$opportunityData", preserveNullAndEmptyArrays: true } },

                // Lookup Client Data
                {
                    $lookup: {
                        from: "Contact",
                        localField: "opportunityData.contractId",
                        foreignField: "_id",
                        as: "clientData",
                        pipeline: [{ $project: { firstName: 1, lastName: 1 } }],
                    },
                },
                { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: true } },

                // Lookup CreatedBy (Member)
                {
                    $lookup: {
                        from: "Member",
                        localField: "createdBy",
                        foreignField: "_id",
                        as: "memberData",
                        pipeline: [{ $project: { _id: 1, name: 1, user: 1 } }],
                    },
                },
                { $unwind: { path: "$memberData", preserveNullAndEmptyArrays: true } },

                // Lookup User Image
                {
                    $lookup: {
                        from: "User",
                        localField: "memberData.user",
                        foreignField: "_id",
                        as: "userData",
                        pipeline: [{ $project: { _id: 1, imageUrl: 1 } }],
                    },
                },
                { $unwind: { path: "$userData", preserveNullAndEmptyArrays: true } },

                // Add createdBy Data (Merged Member + User Image)
                {
                    $addFields: {
                        createdBy: {
                            _id: { $ifNull: ["$memberData._id", null] },
                            name: { $ifNull: ["$memberData.name", "Unknown"] },
                            userImageUrl: {
                                _id: { $ifNull: ["$userData._id", null] },
                                imageUrl: { $ifNull: ["$userData.imageUrl", null] },
                            },
                        },
                    },
                },
                // Final Projection
                {
                    $project: {
                        _id: 1,
                        oppId: 1,
                        PO: "$opportunityData.PO",
                        num: "$opportunityData.num",
                        firstName: "$clientData.firstName",
                        lastName: "$clientData.lastName",
                        name: 1,
                        mimetype: 1,
                        url: 1,
                        thumbnail: 1,
                        tags: 1,
                        location: 1,
                        createdAt: 1,
                        createdBy: 1,
                    },
                },
                { $sort: { createdAt: -1 } },
                {
                    $facet: {
                        paginatedResults: [{ $skip: skip }, { $limit: limit }],
                        totalCount: [{ $count: "count" }],
                    },
                },
            ];

            const result = await this.mediaModel.aggregate(query);
            const paginatedResults = result[0].paginatedResults;
            const totalCount = result[0].totalCount[0]?.count || 0;
            console.log(result, paginatedResults.length, totalCount);

            return new OkResponse({
                media: paginatedResults,
                pagination: {
                    page: getQueryDto?.skip,
                    limit,
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                },
            });
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException("An error occurred while fetching Opportunity Media");
        }
    }

    // for share media url hash

    generateToken(uuid: string) {
        const buffer = Buffer.from(uuid, "utf8");
        // Compress the Buffer using zlib
        const compressed = deflateSync(buffer);
        // Encode the compressed Buffer to a base64url string
        const token = compressed
            .toString("base64")
            .replace(/\+/g, "-")
            .replace(/\//g, "_")
            .replace(/=+$/, "");

        return token;
    }

    // Function to reverse the token back to the original UUID array
    reverseToken(token: string) {
        // Replace URL-friendly characters back to base64 characters
        const base64 = token.replace(/-/g, "+").replace(/_/g, "/");

        // Add padding if necessary
        const padding = base64.length % 4;
        const paddedBase64 = padding ? base64 + "=".repeat(4 - padding) : base64;

        const compressed = Buffer.from(paddedBase64, "base64");
        const buffer = inflateSync(compressed);
        const combinedUuids = buffer.toString("utf8");
        const uuids = combinedUuids.match(/.{36}/g) || [];

        return uuids[0];
    }

    async createShareMedia(companyId: string, shareMediaDto: ShareMediaQueryDto) {
        try {
            const { imageIds, createdBy, tags, types } = shareMediaDto.filters || {};
            const filters: any = imageIds?.length
                ? { imageIds }
                : {
                      ...(createdBy?.length && { createdBy }),
                      ...(tags?.length && { tags }),
                      ...(types?.length && { types }),
                  };

            const data = await this.shareMediaModel.create({
                companyId,
                oppId: shareMediaDto.oppId,
                filters,
            });

            // Delete media older than 90 days
            const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
            this.shareMediaModel.deleteMany({ createdAt: { $lt: ninetyDaysAgo } });

            const hash = this.generateToken(data._id);

            return new OkResponse({ hash });
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getShareMedia(hash: string) {
        try {
            const id = this.reverseToken(hash);
            const { filters, oppId, companyId } = await this.shareMediaModel.findById(
                { _id: id },
                { filters: 1, oppId: 1, companyId: 1 },
            );
            const media = await this.getMedia(companyId, oppId, filters);

            return new OkResponse({ media });
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
