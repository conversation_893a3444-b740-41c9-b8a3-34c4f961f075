import { BadRequestException, HttpException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { AddGpsDto } from "./dto/create-gps.dto";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { GpsDocument } from "./schema/gps.schema";
import OkResponse from "src/shared/http/response/ok.http";
import CreatedResponse from "src/shared/http/response/created.http";
import { FindAllGps } from "./dto/get-all-gps.dto";

@Injectable()
export class GpsService {
    constructor(@InjectModel("Gps") private readonly gpsModel: Model<GpsDocument>) {}

    async addGps(companyId: string, addGpsDtos: AddGpsDto[]) {
        try {
            for (const dto of addGpsDtos) {
                const { memberId, timeCardId, location } = dto;

                const existingDoc = await this.gpsModel.findOne({ memberId, timeCardId });

                if (existingDoc) {
                    await this.gpsModel.updateOne(
                        { _id: existingDoc._id },
                        {
                            $push: {
                                "location.coordinates": {
                                    $each: location.coordinates,
                                },
                            },
                        },
                    );
                } else {
                    const newDoc = {
                        ...dto,
                        companyId,
                    };
                    await this.gpsModel.create(newDoc);
                }
            }

            return new CreatedResponse({ message: "GPS locations added successfully" });
            // const gpsDataWithCompanyId = addGpsDtos.map((dto) => ({
            //     ...dto,
            //     companyId,
            // }));

            // await this.gpsModel.insertMany(gpsDataWithCompanyId);

            return new CreatedResponse({ message: "GPS locations added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findAll(companyId: string, findAllGps: FindAllGps) {
        try {
            const { filter = [], endDate, startDate } = findAllGps;
            if (!filter.length) throw new BadRequestException("Please select atleast one team member");
            const data = await this.gpsModel.aggregate([
                {
                    $match: {
                        companyId,
                    },
                },
                {
                    $addFields: {
                        location: {
                            coordinates: {
                                $filter: {
                                    input: "$location.coordinates",
                                    as: "coord",
                                    cond: {
                                        $let: {
                                            vars: {
                                                ts: {
                                                    $dateFromString: {
                                                        dateString: { $arrayElemAt: ["$$coord", 3] },
                                                    },
                                                },
                                            },
                                            in: {
                                                $and: [
                                                    { $gte: ["$$ts", new Date(startDate)] },
                                                    { $lte: ["$$ts", new Date(endDate)] },
                                                ],
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                {
                    $match: {
                        "location.coordinates.0": { $exists: true },
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        foreignField: "_id",
                        localField: "memberId",
                        as: "memberDetails",
                        pipeline: [
                            {
                                $project: { name: 1, departmentId: 1 },
                            },
                        ],
                    },
                },
                {
                    $unwind: "$memberDetails",
                },
                {
                    $lookup: {
                        from: "Compensation",
                        foreignField: "memberId",
                        localField: "memberId",
                        as: "memberPosition",
                        pipeline: [
                            {
                                $project: { positionId: 1 },
                            },
                        ],
                    },
                },
                {
                    $unwind: { path: "$memberDetails", preserveNullAndEmptyArrays: true },
                },
                {
                    $unwind: { path: "$memberPosition", preserveNullAndEmptyArrays: true },
                },
                {
                    $lookup: {
                        from: "TimeCard",
                        localField: "timeCardId",
                        foreignField: "_id",
                        as: "timeCardInfo",
                        pipeline: [
                            {
                                $project: { _id: 1, projectPO: 1 },
                            },
                        ],
                    },
                },
                {
                    $unwind: { path: "$timeCardInfo", preserveNullAndEmptyArrays: true },
                },
                {
                    $group: {
                        _id: "$memberId",
                        data: { $push: "$$ROOT" },
                        memberName: { $first: "$memberDetails.name" },
                        dept: { $first: "$memberDetails.departmentId" },
                        post: { $first: "$memberPosition.positionId" },
                    },
                },
                {
                    $match: {
                        $or: [{ dept: { $in: filter } }, { post: { $in: filter } }],
                    },
                },
                {
                    $project: {
                        _id: 0,
                        memberId: "$_id",
                        name: "$memberName",
                        data: {
                            $map: {
                                input: "$data",
                                as: "d",
                                in: {
                                    _id: "$$d._id",
                                    // memberId: "$$d.memberId",
                                    timeCardId: "$$d.timeCardId",
                                    location: "$$d.location.coordinates",
                                    projectPO: "$$d.timeCardInfo.projectPO",
                                    // activity: "$$d.activity",
                                    // location: "$$d.location",
                                    // createdAt: "$$d.createdAt",
                                },
                            },
                        },
                    },
                },
            ]);
            console.log(data);
            return new OkResponse({ data });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async recentGpsLocation(companyId: string, memberId: string) {
        try {
            const gpsData: any = await this.gpsModel
                .findOne({
                    memberId,
                })
                .sort({ createdAt: -1 });
            const recentdata = gpsData?.location?.coordinates[gpsData?.location?.coordinates?.length - 1];
            const long = recentdata[1];
            const lat = recentdata[0];
            const recentLocation = { long, lat, memberId: gpsData?.memberId };
            return new OkResponse({ recentLocation });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
