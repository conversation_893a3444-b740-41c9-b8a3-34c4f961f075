import { Body, Controller, Get, Param, ParseUUIDPipe, Patch, Query, UseGuards } from "@nestjs/common";
import { DashboardService } from "./dashboard.service";
import { ApiTags, ApiBearerAuth, ApiOperation, ApiUnauthorizedResponse } from "@nestjs/swagger";
import { Positions } from "src/auth/guards/auth.guard";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { PositionService } from "src/position/position.service";
import { PaginationRequestDto } from "./dto/pagination.dto";
import { UserAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { moduleNames } from "src/shared/constants/constant";

@ApiTags("Dashboard")
@Auth()
@ApiBearerAuth()
// @UseGuards(JwtAuthGuard)
@Controller({ path: "dashboard", version: "1" })
export class DashboardController {
    constructor(
        private readonly dashboardService: DashboardService,
        private readonly positionService: PositionService,
    ) {}

    // TODO to be removed
    //Crew Dashboard
    @ApiOperation({ summary: "Get crew dashboard" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Positions({
    //     name: "salesPerson dashboard",
    //     actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    // })
    @Get("crew/:memberId/:weekStart/:startDate")
    async crewTimeCards(
        @GetUser() user: JwtUserPayload,
        @Param("memberId", ParseUUIDPipe) memberId: string,
        @Param("weekStart") weekStart: Date,
        @Param("startDate") startDate: Date,
    ) {
        return this.dashboardService.crewTimeCards(user.companyId, user.memberId, weekStart, startDate);
    }

    // TODO to be checked
    //Sales Person Dashboard
    @ApiOperation({ summary: "Get back log report" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Positions({
    //     category: "dashboard",
    //     name: moduleNames.dashboard.sales,
    //     actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    // })
    @Get("backlog")
    async backlogCalc(@GetUser() user: JwtUserPayload) {
        return this.dashboardService.backlogCalc(user.companyId);
    }

    // TODO to be removed
    @ApiOperation({ summary: "Get sales Person report dashboard" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Positions({
    //     category: "dashboard",
    //     name: moduleNames.dashboard.sales,
    //     actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    // })
    @Get("sales-person-report/salesPerson/:salesPersonId/:currentDate")
    async salesPersonReport(
        @GetUser() user: JwtUserPayload,
        @Param("salesPersonId", ParseUUIDPipe) salesPersonId: string,
        @Param("currentDate") currentDate: Date,
    ) {
        return this.dashboardService.salesPersonReport(user._id, user.companyId, salesPersonId, currentDate);
    }

    @ApiOperation({ summary: "Get CSR report dashboard" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.leads,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("csr-report/csr/:csrId/:currentDate")
    async csrReport(
        @GetUser() user: JwtUserPayload,
        @Param("csrId", ParseUUIDPipe) csrId: string,
        @Param("currentDate") currentDate: Date,
    ) {
        return this.dashboardService.csrReport(user._id, user.companyId, csrId, currentDate);
    }

    @ApiOperation({ summary: "Get sales Person action list dashboard" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.oppsToDo,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("sales-person-action-list/salesPerson/:salesPersonId")
    async actionList(
        @GetUser() user: JwtUserPayload,
        @Param("salesPersonId", ParseUUIDPipe) salesPersonId: string,
        @Query() paginationRequestDto: PaginationRequestDto,
    ) {
        return this.dashboardService.actionList(user.companyId, salesPersonId, paginationRequestDto);
    }

    @ApiOperation({ summary: "Get sales Person no action list dashboard" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.oppsToDo,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("sales-person-no-action-list/salesPerson/:salesPersonId")
    async noActionList(
        @GetUser() user: JwtUserPayload,
        @Param("salesPersonId", ParseUUIDPipe) salesPersonId: string,
        @Query() paginationRequestDto: PaginationRequestDto,
    ) {
        return this.dashboardService.noActionList(user.companyId, salesPersonId, paginationRequestDto);
    }

    @ApiOperation({ summary: "Get lead action list dashboard" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.leads,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("lead-action-list/csrId/:csrId")
    async leadActionList(
        @GetUser() user: JwtUserPayload,
        @Param("csrId", ParseUUIDPipe) csrId: string,
        @Query() paginationRequestDto: PaginationRequestDto,
    ) {
        return this.dashboardService.leadActionList(
            user.companyId,
            user._id,
            user.memberId,
            csrId,
            paginationRequestDto,
            user.teamPermission,
        );
    }

    @ApiOperation({ summary: "Get sales Person no action list dashboard" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.leads,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("lead-no-action-list/csrId/:csrId")
    async leadNoActionList(
        @GetUser() user: JwtUserPayload,
        @Param("csrId", ParseUUIDPipe) csrId: string,
        @Query() paginationRequestDto: PaginationRequestDto,
    ) {
        return this.dashboardService.leadNoActionList(
            user.companyId,
            user._id,
            user.memberId,
            csrId,
            paginationRequestDto,
            user.teamPermission,
        );
    }

    @ApiOperation({ summary: "Get actions for opp" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.leads,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("lead-actions/csrId/:csrId/dateEnd/:dateEnd")
    async leadActions(
        @GetUser() user: JwtUserPayload,
        @Param("csrId", ParseUUIDPipe) csrId: string,
        @Param("dateEnd") dateEnd: Date,
    ) {
        return this.dashboardService.leadActions(
            user._id,
            user.companyId,
            csrId,
            user.memberId,
            dateEnd,
            user.teamPermission,
        );
    }

    // TODO to be removed
    @ApiOperation({ summary: "Assign selected project to sales person" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Positions({
    //     category: "dashboard",
    //     name: moduleNames.dashboard.sales,
    //     actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    // })
    @Patch("assign-project-to-sales-person/salesPerson/:salesPerson")
    async assignProjectToSalesPerson(
        @GetUser() user: JwtUserPayload,
        @Param("salesPerson", ParseUUIDPipe) salesPerson: string,
        @Body() oppIds: [],
    ) {
        return this.dashboardService.assignProjectToSalesPerson(user.companyId, salesPerson, oppIds);
    }

    @ApiOperation({ summary: "Get actions for opp" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.oppsToDo,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("actions/salesPerson/:salesPerson/dateEnd/:dateEnd")
    async actions(
        @GetUser() user: JwtUserPayload,
        @Param("salesPerson", ParseUUIDPipe) salesPerson: string,
        @Param("dateEnd") dateEnd: Date,
    ) {
        return this.dashboardService.actions(user.companyId, salesPerson, dateEnd);
    }

    //Project Manager Dashboard
    @ApiOperation({ summary: "Get member list not in any crew or in many crew" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.timecardsMissingUnapproved,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("member-not-or-in-many-crew")
    async membersNotInCrewOrInMany(@GetUser() user: JwtUserPayload) {
        return this.dashboardService.membersNotInCrewOrInMany(user.companyId);
    }

    @ApiOperation({ summary: "Get members who didn't clocked-in" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.timecardsMissingUnapproved,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("not-clocked-in/:dayStart")
    async notClockedIn(@GetUser() user: JwtUserPayload, @Param("dayStart") dayStart: Date) {
        const {
            data: { memberPosition: position },
        } = await this.positionService.getMemberPosition(user._id, user.companyId);

        return this.dashboardService.notClockedIn(position, user.companyId, dayStart);
    }

    // TODO to be removed
    //Crew Lead Dashboard
    @ApiOperation({ summary: "Get day report of member" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.timecardsMissingUnapproved,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("day-report-list/dayStart/:dayStart/:dayEnd")
    async dayReport(
        @GetUser() user: JwtUserPayload,
        @Param("dayStart") dayStart: Date,
        @Param("dayEnd") dayEnd: Date,
    ) {
        return this.dashboardService.dayReport(user._id, user.companyId, dayStart, dayEnd);
    }

    @ApiOperation({ summary: "Get date list of unapproved cards" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.timecardsMissingUnapproved,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("unapproved-cards-list/crewLead/:crewLeadId/:dayStart")
    async unapprovedCards(
        @GetUser() user: JwtUserPayload,
        @Param("crewLeadId", ParseUUIDPipe) crewLeadId: string,
        @Param("dayStart") dayStart: Date,
    ) {
        const {
            data: { memberPosition: position },
        } = await this.positionService.getMemberPosition(user._id, user.companyId);

        return this.dashboardService.unapprovedCards(position, user.companyId, crewLeadId, dayStart);
    }

    @ApiOperation({ summary: "Get missing dailogs date" })
    @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    @Positions({
        category: "dashboard",
        name: moduleNames.dashboard.missingDailyLogs,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed],
    })
    @Get("missing-dailylog/:start/:end")
    async missingDailyLogs(
        @GetUser() user: JwtUserPayload,
        @Param("start") start: Date,
        @Param("end") end: Date,
    ) {
        const {
            data: { memberPosition: position },
        } = await this.positionService.getMemberPosition(user._id, user.companyId);

        return this.dashboardService.missingDailyLogs(position, user.companyId, start, end);
    }
}
