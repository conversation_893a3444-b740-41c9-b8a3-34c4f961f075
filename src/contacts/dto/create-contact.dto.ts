import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsArray,
    IsBoolean,
    IsDate,
    IsEnum,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
    IsUUID,
} from "class-validator";
import { Type } from "class-transformer";
import { ContactTypeEnum } from "../enum/contact.enum";

class AttributionDto {
    @ApiProperty()
    @IsString()
    sessionSource: string;

    @ApiProperty()
    @IsString()
    utmSource: string;

    @ApiProperty()
    @IsString()
    utmMedium: string;

    @ApiProperty()
    @IsString()
    utmContent: string;

    @ApiProperty()
    @IsString()
    utmCampaign: string;

    @ApiProperty()
    @IsString()
    utmTerm: string;

    @ApiProperty()
    @IsString()
    utmKeyword: string;

    @ApiProperty()
    @IsString()
    utmMatchType: string;

    @ApiProperty()
    @IsString()
    referringWebpage: string;

    @ApiProperty()
    @IsString()
    adSetId: string;

    @ApiProperty()
    @IsString()
    googleClickId: string;

    @ApiProperty()
    @IsString()
    adName: string;

    @ApiProperty()
    @IsString()
    gaClientId: string;

    @ApiProperty()
    @IsString()
    userAgent: string;

    @ApiProperty()
    @IsString()
    url: string;

    @ApiProperty()
    @IsString()
    ip: string;

    @ApiProperty()
    @IsString()
    adGroupId: string;

    @ApiProperty()
    @IsString()
    gbraId: string;

    @ApiProperty()
    @IsString()
    wbraId: string;

    @ApiProperty()
    @IsString()
    fbr: string;

    @ApiProperty()
    @IsString()
    fbp: string;

    @ApiProperty()
    @IsString()
    form: string;

    @ApiProperty()
    @IsString()
    formId: string;

    @ApiProperty()
    @IsDate()
    @Type(() => Date)
    createdAt: Date;
}

// class TrackingDto {
//     @ApiPropertyOptional({ type: [AttributionDto] })
//     @IsOptional()
//     @IsArray()
//     @Type(() => AttributionDto)
//     previous?: AttributionDto[];
// }

class DndDto {
    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    email?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    sms?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    voice?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    gbp?: boolean;

    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    inbound?: boolean;
}

export class CreateContactDto {
    @ApiProperty({ description: "Full name of the contact" })
    @IsNotEmpty()
    @IsString()
    fullName: string;

    @ApiProperty({ description: "Type of contact", enum: ContactTypeEnum })
    @IsNotEmpty()
    @IsEnum(ContactTypeEnum)
    type: ContactTypeEnum;

    @ApiProperty({ description: "First name of the contact" })
    @IsNotEmpty()
    @IsString()
    firstName: string;

    @ApiPropertyOptional({ description: "Last name of the contact" })
    @IsOptional()
    @IsString()
    lastName?: string;

    @ApiPropertyOptional({ description: "Street address of the contact" })
    @IsOptional()
    @IsString()
    street?: string;

    @ApiPropertyOptional({ description: "City of the contact" })
    @IsOptional()
    @IsString()
    city?: string;

    @ApiPropertyOptional({ description: "State of the contact" })
    @IsOptional()
    @IsString()
    state?: string;

    @ApiPropertyOptional({ description: "Zip code of the contact" })
    @IsOptional()
    @IsString()
    zip?: string;

    @ApiPropertyOptional({ description: "Full address of the contact" })
    @IsOptional()
    @IsString()
    fullAddress?: string;

    @ApiPropertyOptional({ description: "Latitude of the contact" })
    @IsOptional()
    @IsString()
    lat?: number;

    @ApiPropertyOptional({ description: "Longitude of the contact" })
    @IsOptional()
    @IsString()
    long?: number;

    @ApiPropertyOptional({ description: "Phone number of the contact" })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiPropertyOptional({ description: "Email of the contact" })
    @IsOptional()
    @IsString()
    email?: string;

    @ApiPropertyOptional({ description: "Appointment setter ID" })
    @IsOptional()
    @IsUUID()
    appointmentSetter?: string;

    @ApiPropertyOptional({ description: "Sales person ID" })
    @IsOptional()
    @IsUUID()
    salesPersonId?: string;

    @ApiPropertyOptional({ description: "Project manager ID" })
    @IsOptional()
    @IsUUID()
    projectManagerId?: string;

    @ApiPropertyOptional({ description: "Work type ID" })
    @IsOptional()
    @IsString()
    workType?: string;

    @ApiPropertyOptional({ description: "Lead source ID" })
    @IsOptional()
    @IsUUID()
    leadSourceId?: string;

    @ApiPropertyOptional({ description: "Campaign ID" })
    @IsOptional()
    @IsUUID()
    campaignId?: string;

    @ApiPropertyOptional({ description: "Referrer" })
    @IsOptional()
    @IsString()
    referrer?: string;

    @ApiPropertyOptional({ description: "Notes", type: [String] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    note?: string[];

    @ApiPropertyOptional({ description: "Automations", type: [String] })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    automations?: string[];

    // @ApiPropertyOptional({ description: "Do not disturb settings" })
    // @IsOptional()
    // @IsObject()
    // @Type(() => DndDto)
    // dnd?: DndDto;

    // @ApiPropertyOptional({ type: [AttributionDto], description: "Tracking information" })
    // @IsOptional()
    // @IsArray()
    // @Type(() => AttributionDto)
    // tracking?: AttributionDto[];

    @ApiPropertyOptional({ description: "Invalid lead reason" })
    @IsOptional()
    @IsString()
    invalidLeadReason?: string;

    @ApiPropertyOptional({ description: "Lost reason" })
    @IsOptional()
    @IsString()
    lostReason?: string;

    @ApiPropertyOptional({ description: "Date of birth" })
    @IsOptional()
    @IsDate()
    @Type(() => Date)
    dateOfBirth?: Date;

    // Lead fields
    @ApiPropertyOptional({ description: "Status" })
    @IsOptional()
    @IsString()
    status?: string;

    @ApiPropertyOptional({ description: "New lead date" })
    @IsOptional()
    @IsDate()
    @Type(() => Date)
    newLeadDate?: Date;

    @ApiPropertyOptional({ description: "Comments" })
    @IsOptional()
    @IsArray()
    comments?: any[];

    @ApiPropertyOptional({ description: "Steps checklist" })
    @IsOptional()
    @IsUUID()
    csrId?: string;

    @ApiPropertyOptional({ description: "Referred by" })
    @IsOptional()
    @IsUUID()
    referredBy?: string;
}
