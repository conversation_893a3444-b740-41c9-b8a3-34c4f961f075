import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional, IsEnum } from "class-validator";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { PaginationDto } from "src/shared/dto/pagination.dto";

export class FetchLeadDto extends PaginationDto {
    @ApiPropertyOptional({ enum: StageGroupEnum })
    @IsOptional()
    @IsEnum(StageGroupEnum)
    stageGroup?: StageGroupEnum;

    @ApiPropertyOptional()
    @IsOptional()
    @Transform(({ value }) => value === "true")
    lost?: boolean;

    @ApiPropertyOptional({ description: "for active & inactive opps" })
    @IsOptional()
    status?: string;
}
