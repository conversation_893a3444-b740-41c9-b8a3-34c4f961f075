import {
    <PERSON>,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    ParseUUI<PERSON>ipe,
    UseGuards,
} from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiTags,
    ApiOperation,
    ApiParam,
    ApiBody,
    ApiQuery,
    ApiResponse,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";

import { LeadService } from "./lead.service";
import { CreateLeadDto } from "./dto/create-lead.dto";
import { UpdateLeadDto } from "./dto/update-lead.dto";
import HttpResponse from "src/shared/http/response/response.http";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { FetchLeadDto } from "./dto/fetch-lead.dto";
import { Positions } from "src/auth/guards/auth.guard";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { CreateNewLeadActionDto } from "./dto/create-action.dto";
import { CreateLeadCommentDto } from "./dto/create-lead-comment.dto";
import { UpdateLeadCommentDto } from "./dto/update-lead-comment.dto";
import { DeleteLeadCommentDto } from "./dto/delete-lead-comment.dto";
import { UpdateLeadChecklistDto } from "./dto/update-lead-checklist.dto";
import { UpdateLeadActivityDto } from "./dto/update-lead-activity.dto";
import { UpdateLeadStatusDto } from "./dto/update-lead-status.dto";
import { LostUnLostLeadDto } from "./dto/lost-unlost-lead.dto";
import { GetLeadDto } from "./dto/get-lead.dto";
import { UpdateOpportunityCheckpointDto } from "src/opportunity/dto/update-opportunity-checkpoint.dto";
import { UpdateOppStageDto } from "src/opportunity/dto/update-opportunity-stage.dto";
import { DeleteOpportunityCheckpointDto } from "src/opportunity/dto/delete-opportunity-checkpoint.dto";
import { PositionService } from "src/position/position.service";
import { moduleNames } from "src/shared/constants/constant";
import { ApiKeyGuard } from "src/auth/guards/api-key.guard";

@ApiTags("Lead")
@ApiBearerAuth()
@Auth()
@Positions({
    category: "crm",
    name: moduleNames.crm.leads,
    actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
})
@Controller({ path: "lead", version: "1" })
export class LeadController {
    constructor(
        private readonly positionService: PositionService,
        private readonly leadService: LeadService,
    ) {}

    // @ApiOperation({ summary: "Create a new lead" })
    // @ApiBody({ type: CreateLeadDto })
    // @ApiResponse({ status: 201, description: "Lead created successfully." })
    // @ApiResponse({ status: 400, description: "Bad reqOuest." })
    // @Post("create-lead")
    // async createLead(
    //     @Body() createLeadDto: CreateLeadDto,
    //     @GetUser() user: JwtUserPayload,
    // ): Promise<HttpResponse> {
    //     return this.leadService.createLead(createLeadDto, user.companyId);
    // }

    // @ApiOperation({ summary: "Fetch a lead by ID" })
    // @ApiParam({ name: "id", description: "Lead ID" })
    // @ApiResponse({ status: 200, description: "Lead retrieved successfully." })
    // @ApiResponse({ status: 404, description: "Lead not found." })
    // @Get("fetch-lead/:id/deleted/:deleted")
    // async findLeadById(
    //     @Param("id", ParseUUIDPipe) id: string,
    //     @Param("deleted") deleted: boolean,
    //     @GetUser() user: JwtUserPayload,
    // ): Promise<HttpResponse> {
    //     return this.leadService.findLeadById(id, user.companyId, deleted);
    // }

    @ApiOperation({ summary: "Fetch all leads based on filter query" })
    @ApiQuery({ name: "deleted", description: "Filter by deleted status" })
    @ApiResponse({ status: 200, description: "Leads retrieved successfully." })
    @Get("all/deleted/:deleted")
    async findAllLeads(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() leadQuery: FetchLeadDto,
    ): Promise<HttpResponse> {
        return this.leadService.findAllLeads(
            user.companyId,
            deleted,
            leadQuery,
            user.memberId,
            user.teamPermission,
        );
    }

    @ApiOperation({ summary: "Update a lead" })
    @ApiParam({ name: "id", description: "Lead ID" })
    @ApiBody({ type: UpdateLeadDto })
    @ApiResponse({ status: 200, description: "Lead updated successfully." })
    @ApiResponse({ status: 404, description: "Lead not found." })
    @Patch("id/:id")
    async updateLead(
        @Param("id", ParseUUIDPipe) id: string,
        @Body() updateLeadDto: UpdateLeadDto,
        @GetUser() user: JwtUserPayload,
    ): Promise<HttpResponse> {
        return this.leadService.updateLead(id, user.companyId, updateLeadDto);
    }

    // @ApiOperation({ summary: "Soft delete a lead" })
    // @ApiParam({ name: "id", description: "Lead ID" })
    // @ApiResponse({ status: 200, description: "Lead soft deleted successfully." })
    // @ApiResponse({ status: 404, description: "Lead not found." })
    // @Delete("delete-lead/:id")
    // async deleteLead(
    //     @Param("id", ParseUUIDPipe) id: string,
    //     @GetUser() user: JwtUserPayload,
    // ): Promise<HttpResponse> {
    //     return this.leadService.deleteLead(id, user.companyId);
    // }

    // @ApiOperation({ summary: "Restore a soft-deleted lead" })
    // @ApiParam({ name: "id", description: "Lead ID" })
    // @ApiResponse({ status: 200, description: "Lead restored successfully." })
    // @ApiResponse({ status: 404, description: "Lead not found." })
    // @Patch("restore-lead/:id")
    // async restoreLead(
    //     @Param("id", ParseUUIDPipe) id: string,
    //     @GetUser() user: JwtUserPayload,
    // ): Promise<HttpResponse> {
    //     return this.leadService.restoreLead(id, user.companyId);
    // }

    // @ApiOperation({ summary: "Permanently delete a lead" })
    // @ApiParam({ name: "id", description: "Lead ID" })
    // @ApiResponse({ status: 200, description: "Lead permanently deleted successfully." })
    // @ApiResponse({ status: 404, description: "Lead not found." })
    // @Delete("perm-delete/:id")
    // async permDeleteLead(
    //     @Param("id", ParseUUIDPipe) id: string,
    //     @GetUser() user: JwtUserPayload,
    // ): Promise<HttpResponse> {
    //     return this.leadService.permDeleteLead(id, user.companyId);
    // }

    // @ApiOperation({ summary: "Create New Action" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Post("create-new-action")
    // async createNewAction(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() createNewActionDto: CreateNewLeadActionDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.createNewAction(user.companyId, createNewActionDto);
    // }

    // @ApiOperation({ summary: "Update Action" })
    // @ApiNotFoundResponse({ description: "Lead not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Patch("update-action")
    // async completeAction(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() createNewActionDto: CreateNewLeadActionDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.completeAction(user.companyId, createNewActionDto);
    // }

    // /**
    //  * Add lead to comment
    //  * @param userId - The ID of the user making the request.
    //  * @param createLeadCommentDto - The DTO containing information to create lead comment
    //  * @returns - A promise that resolves to an HTTP response.
    //  */
    // @ApiOperation({ summary: "Create New Comment" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Post("create-lead-comment")
    // async createLeadComment(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() createLeadCommentDto: CreateLeadCommentDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.createLeadComment(user.companyId, createLeadCommentDto);
    // }

    // @ApiOperation({ summary: "Update Lead comment" })
    // @ApiNotFoundResponse({ description: "Lead not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Patch("update-lead-comment")
    // async updateLeadComment(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() updateLeadCommentDto: UpdateLeadCommentDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.updateLeadComment(user.companyId, updateLeadCommentDto);
    // }

    // /**
    //  * Delete Lead comment
    //  * @param userId - The ID of the user making the request.
    //  * @param deleteLeadCommentDto - The DTO containing information to delete lead comment
    //  * @returns - A promise that resolves to an HTTP response.
    //  */
    // @ApiOperation({ summary: "Delete Lead Comment" })
    // @ApiNotFoundResponse({ description: "Lead not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Delete("delete-lead-comment")
    // async deleteLeadComment(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() deleteLeadCommentDto: DeleteLeadCommentDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.deleteLeadComment(user._id, deleteLeadCommentDto);
    // }

    // @ApiOperation({ summary: "Update checklist" })
    // @ApiNotFoundResponse({ description: "Lead not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Patch("update-checklist")
    // async updateChecklist(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() updateChecklistDto: UpdateLeadChecklistDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.updateChecklist(user._id, updateChecklistDto);
    // }

    // @ApiOperation({ summary: "Update Lead Activity" })
    // @ApiNotFoundResponse({ description: "Lead not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Patch("update-lead-activity")
    // async updateLeadActivity(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() updateLeadActivityDto: UpdateLeadActivityDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.updateLeadActivity(user.companyId, updateLeadActivityDto);
    // }

    @ApiOperation({ summary: "Change Status Lead Active" })
    @ApiNotFoundResponse({ description: "Lead not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("active/:id")
    async activeLead(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
    ): Promise<HttpResponse> {
        return this.leadService.activeLead(id, user.companyId, user.memberId);
    }

    @ApiOperation({ summary: "Change Status Lead Invalid" })
    @ApiNotFoundResponse({ description: "Lead not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("invalid/:id")
    async leadInvalidStatus(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
        @Body() updateLeadStatusDto: UpdateLeadStatusDto,
    ): Promise<HttpResponse> {
        return this.leadService.leadInvalidStatus(id, user.companyId, user.memberId, updateLeadStatusDto);
    }

    @ApiOperation({ summary: "Lost Lead" })
    @ApiNotFoundResponse({ description: "Lead not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("lost/:id")
    async lostLead(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
        @Body() lostUnLostLeadDto: LostUnLostLeadDto,
    ): Promise<HttpResponse> {
        return this.leadService.lostLead(user.companyId, user.memberId, id, lostUnLostLeadDto);
    }

    @ApiOperation({ summary: "UnLost Lead" })
    @ApiNotFoundResponse({ description: "Lead not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch("un-lost/:id")
    async unLostLead(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
        @Body() lostUnLostLeadDto: LostUnLostLeadDto,
    ): Promise<HttpResponse> {
        return this.leadService.unLostLead(user.companyId, user.memberId, id, lostUnLostLeadDto);
    }

    // @ApiOperation({ summary: "Get Lead completion percent" })
    // @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Get("lead-completion-percent/deleted/:deleted")
    // async getLeadCompletionPercent(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("deleted") deleted: boolean,
    //     @Query() getLeadDto: GetLeadDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.getLeadCompletionPercent(user._id, user.companyId, deleted, getLeadDto);
    // }

    // @ApiOperation({ summary: "Get Lead step checklist for a stage" })
    // @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Get("lead-step-checklist/leadId/:leadId/stageId/:stageId")
    // async getLeadStepChecklistByStage(
    //     @Param("leadId", ParseUUIDPipe) leadId: string,
    //     @Param("stageId", ParseUUIDPipe) stageId: string,
    // ): Promise<HttpResponse> {
    //     return this.leadService.getLeadStepChecklistByStage(leadId, stageId);
    // }

    // @ApiOperation({ summary: "Get Lead step checklist for a stage" })
    // @ApiUnauthorizedResponse({ description: "Unauthorized request" })
    // @Post("convert-to-opportunity/leadId/:leadId/oppId/:oppId/date/:date")
    // async convertToOpportunity(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("leadId", ParseUUIDPipe) leadId: string,
    //     @Param("oppId", ParseUUIDPipe) oppId: string,
    //     @Param("date") date: Date,
    // ): Promise<HttpResponse> {
    //     return this.leadService.convertToOpportunity(user.companyId, user.memberId, leadId, oppId, date);
    // }

    // @ApiOperation({ summary: "Update Lead Checkpoint" })
    // @ApiNotFoundResponse({ description: "Lead not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Patch("update-lead-checkpoint")
    // async updateLeadCheckpoint(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() updateOppCheckpointDto: UpdateOpportunityCheckpointDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.updateLeadCheckpoint(user.companyId, updateOppCheckpointDto);
    // }

    @ApiOperation({ summary: "Update Lead Stage" })
    @ApiNotFoundResponse({ description: "Lead not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Patch(":id/stage/:stageId")
    async updateLeadStage(
        @GetUser() user: JwtUserPayload,
        @Param("id", ParseUUIDPipe) id: string,
        @Param("stageId", ParseUUIDPipe) stageId: string,
    ): Promise<HttpResponse> {
        return this.leadService.updateLeadStage(user.companyId, user.memberId, id, stageId);
    }

    // @ApiOperation({ summary: "Delete Lead Checkpoint" })
    // @ApiNotFoundResponse({ description: "Lead not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Patch("delete-lead-checkpoint")
    // async deleteLeadCheckpoint(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() deleteLeadCheckpointDto: DeleteOpportunityCheckpointDto,
    // ): Promise<HttpResponse> {
    //     return this.leadService.deleteLeadCheckpoint(user.companyId, deleteLeadCheckpointDto);
    // }
}

@ApiTags("Lead")
@Controller({ path: "lead", version: "1" })
@UseGuards(ApiKeyGuard)
export class LeadControllerApiKey {
    constructor(private readonly leadService: LeadService) {}

    @Post("from-Zapier")
    async createLeadFromZapier(@Body() data: Record<string, any>): Promise<HttpResponse> {
        console.log("Inside zapier", new Date());
        console.log(data);
        // const finalData = data?.email ? data : data?.contact;
        return this.leadService.createLeadFromZapier(data);
    }

    @Post("zapier-appointment")
    async convertZapierLeadToOpp(@Body() data: Record<string, any>) {
        console.log("zapier appointment", new Date());
        console.log(data);
    }
}
