import { WeekEnum } from "src/company/enum/week.enum";
import { WageIntervalEnum } from "src/compensation/enum/wage-interval.enum";
import { Wage } from "src/compensation/schema/compensation.schema";

export function startOfYesterday() {
    const d = new Date();
    const start = new Date(d.setHours(0, 0, 0, 0));
    start.setDate(start.getDate() - 1);
    return start;
}

export function sixMonthsAgo(date: Date) {
    const sixMonthsAgoDate = new Date(date);
    sixMonthsAgoDate.setMonth(sixMonthsAgoDate.getMonth() - 6);
    sixMonthsAgoDate.setUTCHours(0, 0, 0, 0);
    return sixMonthsAgoDate;
}

export function endOfToday() {
    const d = new Date();
    // end.setUTCHours(23, 59, 59, 999);
    const end = new Date(d.setHours(23, 59, 59, 999));
    return end;
}

export function dateLessThan(date1, date2) {
    if (new Date(date1) < new Date(date2)) {
        return true;
    } else {
        return false;
    }
}

export function startOfDate(date) {
    const d = new Date(date);
    // start.setUTCHours(0, 0, 0, 0);
    const start = new Date(d.setHours(0, 0, 0, 0));
    return start;
}

export function endOfDate(date) {
    const d = new Date(date);
    // end.setUTCHours(23, 59, 59, 999);

    const end = new Date(d.setHours(23, 59, 59, 999));

    return end;
}

export function activeCrewMember(member, start, end) {
    if (member.startDate <= end && (!member.removeDate || member.removeDate >= start)) {
        return true;
    } else {
        return false;
    }
}

// Reusable function to deduplicate an array of objects
export function dedupeObjects(array: { [key: string]: any }[]) {
    const stringifiedArray = array.map((item) => JSON.stringify(item));
    const uniqueStringifiedArray = dedupeArray(stringifiedArray);
    return uniqueStringifiedArray.map((item) => JSON.parse(item));
}

export function dedupePwObjects(array: { workTask: string; date: string }[]) {
    const uniqueItems = new Map<string, { workTask: string; date: string }>();

    array.forEach((item) => {
        if (!uniqueItems.has(item.date)) {
            uniqueItems.set(item.date, item);
        }
    });

    return Array.from(uniqueItems.values());
}

export function dedupeArray(array) {
    if (!array) return [];
    return array.filter(function (item, index) {
        return array.indexOf(item) >= index;
    });
}

export function dynamicSort(property) {
    let sortOrder = 1;
    if (property[0] === "-") {
        sortOrder = -1;
        property = property.substr(1);
    }
    return function (a, b) {
        const result = a[property] < b[property] ? -1 : a[property] > b[property] ? 1 : 0;
        return result * sortOrder;
    };
}

// export function arrayTotal(array, property) {
//     const sum = (items, prop) => items.reduce((total, item) => total + (item[prop] || 0), 0);
//     return roundTo2(sum(array, property));
// }

// Calculate the total of a property in an array
export function sumArray(array, property) {
    if (!array?.length) return 0;

    const sum = (items, prop) => items.reduce((total, item) => total + (item[prop] || 0), 0);

    return roundTo2(sum(array, property));
}
// Calculate the total of a property in an existing array
export function add2Array(array1, array2) {
    const maxLength = Math.max(array1.length, array2.length);
    const result = [];

    for (let i = 0; i < maxLength; i++) {
        const val1 = array1[i] !== undefined ? array1[i] : 0;
        const val2 = array2[i] !== undefined ? array2[i] : 0;
        result.push(val1 + val2);
    }

    return result;
}

// Takes an array of member's (above) and returns the lead pay for that day
// USE: Subtract pwTotal and add in this to get total pay for lead
// EDIT: 11/26/22 Change Foreman pay to piece work + 15% of member's pw
export function calcCrewLeadBonus(members) {
    let leadPay = 0;
    const leadMember = members?.find((member) => member.crewLead);
    const crewBonus = leadMember?.crewPieceWork || 0;

    members?.forEach((member) => {
        leadPay += member.crewLead ? 0 : (member.pwTotal - member.removeFromLead) * crewBonus;
    });
    return roundTo2(leadPay);
}

// make a PO# out of the last name and address of project
export function createPO(name: string, address: string) {
    // Process name
    name = name
        .replace(/[\W_]+/g, "")
        .slice(0, 4)
        .toUpperCase();

    // Process address
    address = address ? address.split(" ")[0] : "";
    address = address.padStart(4, "0").slice(0, 4);
    // Generate PO
    const po = name + address;
    return po;
}

// Return short date M/D/YY h:mm am
export function shortenDateTime(date) {
    return `${date.getMonth() + 1}/${date.getDate()}/${date
        .getFullYear()
        .toString()
        .substr(-2)} ${get12HrTimeFromDate(date)}`;
}

//convert Date object to readable date time: 'M/DD/YYYY HH:MM am'
export function get12HrTimeFromDate(date) {
    let hours = date.getHours();
    const minutes = pad(date.getMinutes());
    const ampm = hours >= 12 ? "pm" : "am";
    hours = hours % 12;
    hours = hours ? hours : 12; // change hour '0' to '12'
    const strTime = `${hours}:${minutes} ${ampm}`;
    return strTime;
}

// if number is less than 10, add leading 0
export function pad(n) {
    return n < 10 ? "0" + n : n;
}

export function loadInputArray(inputInputs: any[]) {
    const inputs = [];
    let i = 0;
    let y;
    while (i < inputInputs.length) {
        y = i + 1;
        const oper = y === 1 ? "+" : inputInputs[i].oper;
        inputs.push({
            iNum: "InputId" + y,
            input: inputInputs[i].id,
            oper,
        });
        i++;
    }
    return inputs;
}

export function loadMatArray(matInputs: any[]) {
    const mats = [];
    let i = 0;
    let y;
    while (i < matInputs.length) {
        y = i + 1;
        mats.push({
            tMat: "MatId" + y,
            mat: matInputs[i].id,
            cov: matInputs[i].cov,
        });
        i++;
    }
    return mats;
}

export function loadLaborArray(laborInputs: any[]) {
    const labor = [];
    let i = 0;
    let y;
    while (i < laborInputs.length) {
        y = i + 1;
        labor.push({
            tLabor: "LaborId" + y,
            worker: laborInputs[i].id,
            time: laborInputs[i].time,
            mod: laborInputs[i].mod,
        });
        i++;
    }
    return labor;
}

export function operator(oldVal, newVal, oper) {
    switch (oper) {
        case "x":
            return oldVal * newVal;
        case "/":
            return oldVal / newVal;
        case "+":
            return oldVal + newVal;
        case "-":
            return oldVal - newVal;
        default:
            console.error("Unexpected operator:", oper);
            return oldVal;
    }
}

// Find average pitch of a project
export function averagePitch(project) {
    let squares = 0;
    let product = 0;
    project?.customData?.reroofAreas?.map((area) => {
        squares += area.install;
        product += area.pitch * area.install;
    });
    return roundTo2(product / squares) || project?.customData?.pitch || 0;
}

// Find pitch mod value for pitch to multiply with
export function getPitchMod(pitches: object, avgPitch: number) {
    const pitchMods = {};
    for (const key in pitches) {
        if (Object.hasOwnProperty.call(pitches, key)) {
            const modifiedKey = key.split("/")[0];
            pitchMods[modifiedKey] = pitches[key];
        }
    }
    const pitchMod = Math.ceil(avgPitch) >= 20 ? 1 + pitchMods["20"] : 1 + pitchMods[Math.ceil(avgPitch)];
    return pitchMod || 1;
}

//In an array of objects, find object with property of highest value
export function findHighestValueInArray(array, prop) {
    let highest;
    if (array.length > 0) {
        highest = array.reduce(function (prev, current) {
            return prev[prop] > current[prop] ? prev : current;
        });
    }
    return highest;
}

// Find average layers of a project
export function averageLayers(project) {
    let squares = 0;
    let product = 0;
    project?.customData?.reroofAreas?.map((area) => {
        squares += area.remove;
        product += area.remove * area.layers;
    });
    return roundTo2(product / squares);
}

// Round a number to 1 decimal
export function roundTo1(num) {
    if (!num) return 0;
    return Math.round(num * 10) / 10;
}
// Round a number to 2 decimals
export function roundTo2(num) {
    if (!num) return 0;
    return Math.round(num * 100) / 100;
}
// Round a number to 3 decimals
export function roundTo3(num) {
    if (!num) return 0;
    return Math.round(num * 1000) / 1000;
}

// Find active crew lead Id on a certain date
export function findCrewLeadId(crew, date) {
    // Find all leads on or before date
    const leadHistory = crew?.leadHistory?.filter((history) => history.promotionDate <= date) || [];
    const lead = findHighestValueInArray(leadHistory, "promotionDate");
    return lead?.leadMemberId;
}

// Compare 2 unique arrays to see if they have the same values
// Arrays must be unique and single values (no objects)
export function arraysAreEqual2(array1, array2) {
    if (!array1 && !array2) return true;
    if (array1?.length !== array2?.length) return false;
    for (const val of array1) {
        if (!array2?.includes(val)) return false;
    }
    return true;
}
export function arraysAreEqual(array1, array2) {
    if (!array1 && !array2) return { equal: true, nonMatchingValues: [] };

    const nonMatchingValues = [];
    for (const val of array1) {
        if (!array2?.includes(val)) nonMatchingValues.push(val);
    }
    const nonMatchingLog = [];
    for (const val of array2) {
        if (!array1?.includes(val)) nonMatchingLog.push(val);
    }

    if (nonMatchingValues.length === 0 && nonMatchingLog.length === 0)
        return { equal: true, nonMatchingValues: [] };
    else return { equal: false, nonMatchingValues, nonMatchingLog };
}

// Return short date M/D/YY
export function shortenDate(date) {
    return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear().toString().substr(-2)}`;
}

// Takes a user's wage array and date, finds the current wage on that date
// eslint-disable-next-line @typescript-eslint/ban-types
export function findCurrentWage(wage: any[], date: Date): Wage | null {
    if (!wage || wage.length === 0) return null;

    const wageArray = wage
        .filter((cw) => cw.effectivePayPeriod && new Date(cw.effectivePayPeriod) <= date)
        .sort((a, b) => {
            const dateComparison =
                new Date(b.effectivePayPeriod).getTime() - new Date(a.effectivePayPeriod).getTime();
            if (dateComparison !== 0) return dateComparison;
            return new Date(b.modifiedAt).getTime() - new Date(a.modifiedAt).getTime();
        });

    return wageArray.length > 0 ? wageArray[0] : null;
}

// Takes a user's wage array and date, finds the next current wage after that date
// eslint-disable-next-line @typescript-eslint/ban-types
export function findNextCurrentWage(wages, date): Wage | null {
    if (!wages || wages.length === 0) return null;

    const wageArray = wages.filter((w) => w.effectivePayPeriod && new Date(w.effectivePayPeriod) > date);
    if (wageArray.length === 0) return null;

    return wageArray.reduce((prev, current) =>
        new Date(prev.effectivePayPeriod) < new Date(current.effectivePayPeriod) ? prev : current,
    );
}

export function formatDate(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return year + "-" + pad(month) + "-" + pad(day);
}

export function getWeekStartOld(weekStartDay, date: Date) {
    // const date = new Date(onDate);
    // date.setHours(0, 0, 0, 0); // set time to midnight
    const day = date.getDay(); // get day of week
    if (day !== Object.values(WeekEnum).indexOf(weekStartDay)) {
        // if today is not week start
        let diff = day - Object.values(WeekEnum).indexOf(weekStartDay); // find difference between today and week start
        if (diff < 0) {
            // if difference is negative, add 7 to it to find how many days away
            diff = diff + 7;
        }
        date.setDate(date.getDate() - diff); // set date to start of week
    }
    return date;
}

export function getWeekStart(weekStartDays: any, date: Date) {
    // date.setHours(0, 0, 0, 0);

    const day = date.getDay(); // get day of week

    // Find the closest week start day
    const closestWeekStartDayIndex = weekStartDays.findIndex(
        (startDay) => day === Object.values(WeekEnum).indexOf(startDay),
    );

    if (closestWeekStartDayIndex === -1) {
        // If today is not any of the week start days
        const closestStartDay = weekStartDays[0];
        let diff = day - Object.values(WeekEnum).indexOf(closestStartDay); // find difference between today and closest week start day
        if (diff < 0) {
            // if difference is negative, add 7 to it to find how many days away
            diff = diff + 7;
        }
        date.setDate(date.getDate() - diff); // set date to start of week
    }

    return date;
}

export function getDateOfMonth(date: number) {
    const currentDate = new Date();
    // Create a new date object set to the first day of the current month
    const startOfMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), date);

    // Set hours, minutes, seconds, and milliseconds to zero for consistency
    startOfMonthDate.setHours(0, 0, 0, 0);

    return startOfMonthDate;
}

export function getEndOfMonth(date = new Date()) {
    // Create a new date object set to the first day of the next month
    const endOfMonthDate = new Date(date.getFullYear(), date.getMonth(), 0);

    // Set hours, minutes, seconds, and milliseconds to zero for consistency
    endOfMonthDate.setHours(0, 0, 0, 0);

    return endOfMonthDate;
}

export function getLastSaturday() {
    const today = new Date();
    const lastSaturday = new Date(today);
    lastSaturday.setDate(today.getDate() - ((today.getDay() + 1) % 7));
    lastSaturday.setHours(0, 0, 0, 0); // Set to midnight
    return lastSaturday;
}

export function getNextFriday(fromDate: Date) {
    const nextFriday = new Date(fromDate);
    nextFriday.setDate(fromDate.getDate() + ((12 - fromDate.getDay()) % 7));
    nextFriday.setHours(0, 0, 0, 0); // Set to midnight
    return nextFriday;
}

export function isWeekend(companyWeekend: any, today: Date) {
    const allWeekDays = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    const dayOfWeekIndex = today.getDay();
    const weekDayName = allWeekDays[dayOfWeekIndex];

    const isWeekend = companyWeekend.includes(weekDayName);

    return isWeekend;
}

// Find the hourly rate with a wage amount and an interval paid
export function getHourlyPay(amount: number, interval: WageIntervalEnum) {
    let wage = 0;
    if (interval === WageIntervalEnum.Hour) {
        wage = amount;
    } else if (interval === WageIntervalEnum.Month) {
        wage = roundTo2(amount / 173.33);
    } else if (interval === WageIntervalEnum.Year) {
        wage = roundTo2(amount / 2080);
    }
    return wage;
}

// Find total days in a month
export function daysInMonth(y, m) {
    return new Date(y, m + 1, 0).getDate();
}

export function toCamelCase(str: string) {
    const camelCase = str.replace(/[-_\s]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ""));
    return camelCase.charAt(0).toLowerCase() + camelCase.slice(1);
}

// Helper function to check if a string is a UUID
export function isUUID(str) {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(str);
}

export const formatNumberToCommaS = (number: number | string | null | undefined): string => {
    if (number === null || number === undefined) {
        return "--"; // Handle null or undefined values if needed
    }
    // Convert to number if it's a string
    const numericValue = typeof number === "string" ? parseFloat(number) : number;
    if (isNaN(numericValue)) {
        return "--"; // Handle non-numeric values
    }
    // Format the number with commas and two decimal places
    const formattedNumber = numericValue.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
    return formattedNumber;
};

export const getTextChange = (newValues: Record<string, any>, oldValues: Record<string, any>) => {
    const diffWords = (oldText: string, newText: string): string => {
        const oldWords = oldText.split(/[ \t]+/);
        const newWords = newText.split(/[ \t]+/);

        let result = "";
        let i = 0,
            j = 0;

        while (i < oldWords.length || j < newWords.length) {
            if (oldWords[i] !== newWords[j]) {
                if (oldWords[i]) {
                    result += `~~${oldWords[i]}~~ `;
                }
                if (newWords[j]) {
                    result += `${newWords[j]} `;
                }
            } else {
                result += `${oldWords[i]} `;
            }
            i++;
            j++;
        }

        return result.trim();
    };

    return Object.entries(newValues).reduce((acc: Record<string, any>, [key, newValue]) => {
        const oldValue = oldValues[key];
        const hasChanged = oldValue !== newValue;

        if (hasChanged) {
            acc[key] = diffWords(oldValue, newValue);
        }

        return acc;
    }, {});
};

export const getInitials = (text: string) => {
    // Split the name into words by spaces
    const words = text.split(" ");

    // Map each word to its first letter and join them into a string
    const initials = words.map((word) => word[0].toUpperCase()).join("");

    return initials;
};

export const profitScoreCalc = (
    soldValue = 0,
    mTotal = 0,
    lTotal = 0,
    commission = 0,
    financeFee = 0,
): number => {
    if (soldValue <= 0) return;
    const totalCosts = mTotal + lTotal + commission + financeFee;
    const profitMargin = (soldValue - totalCosts) / lTotal;

    return roundTo1(profitMargin * 10 - 21);
};

// Utility function to calculate total materials, labor, and job costs from change orders
export const calculateTotalCosts = (
    changeOrders: {
        signedBySales: boolean;
        jobCost: number;
        materials: number;
        labor: number;
        deleted?: boolean;
    }[],
    changeOrderDto: { materials: number; labor: number; jobCost: number; deleted?: boolean },
) => {
    let totalMaterials = 0;
    let totalLabor = 0;
    let totalJobCost = 0;

    if (Array.isArray(changeOrders)) {
        changeOrders
            .filter((order) => !order.deleted)
            .forEach((order) => {
                if (order.signedBySales || order.jobCost < 0) {
                    totalMaterials += order.materials ?? 0;
                    totalLabor += order.labor ?? 0;
                    totalJobCost += order.jobCost ?? 0;
                }
            });
    }

    return { totalMaterials, totalLabor, totalJobCost };
};

export const buildMongoQuery = (filterPayload: { filter: any[]; logic: string }): any => {
    const { filter, logic } = filterPayload;
    const mongoFilters = filter.reduce((acc, f) => {
        switch (f.operator) {
            case "is":
                if (acc[f.field]) {
                    if (Array.isArray(acc[f.field].$in)) {
                        acc[f.field].$in.push(f.value);
                    } else {
                        acc[f.field] = { $in: [acc[f.field], f.value] };
                    }
                } else {
                    acc[f.field] = f.value;
                    //{ $regex: f.value, $options: "i" };
                }
                break;
            case "is_not":
                acc[f.field] = { $ne: f.value };
                break;
            case "is_empty":
                acc[f.field] = { $in: [null, ""] };
                break;
            case "is_not_empty":
                acc[f.field] = { $exists: true, $nin: [null, ""] };
                break;
            case "more":
                if (f.unit === "days") {
                    const date = new Date();
                    date.setDate(date.getDate() - f.value);
                    acc[f.field] = { $gt: date };
                } else {
                    acc[f.field] = { $gt: f.value };
                }
                break;
            case "less":
                acc[f.field] = { $lt: f.value };
                break;
            case "range":
                acc[f.field] = { $gte: f?.startDate, $lte: f?.endDate };
                break;
        }
        return acc;
    }, {});

    return logic === "AND"
        ? mongoFilters
        : { $or: Object.entries(mongoFilters).map(([key, value]) => ({ [key]: value })) };
};

export const getActualCostBetweenDates = (sourceArray, dateStart, dateEnd) => {
    dateStart = new Date(Date.UTC(dateStart.getFullYear(), dateStart.getMonth(), dateStart.getDate()));
    dateEnd = new Date(Date.UTC(dateEnd.getFullYear(), dateEnd.getMonth(), dateEnd.getDate()));

    for (const item of sourceArray) {
        let totalCost = 0;

        if (Array.isArray(item.actualCost)) {
            for (const entry of item.actualCost) {
                const { year, month, cost } = entry;
                const daysInMonth = new Date(year, month, 0).getDate();
                const perDayCost = cost / daysInMonth;

                const monthStart = new Date(Date.UTC(year, month - 1, 1));
                const monthEnd = new Date(Date.UTC(year, month - 1, daysInMonth));
                const overlapStart = dateStart > monthStart ? dateStart : monthStart;
                const overlapEnd = dateEnd < monthEnd ? dateEnd : monthEnd;

                if (overlapStart <= overlapEnd) {
                    const daysOverlap = Math.floor((overlapEnd - overlapStart) / (1000 * 60 * 60 * 24)) + 1;
                    totalCost += perDayCost * daysOverlap;
                }
            }
        }

        item.totalCost = Number(totalCost.toFixed(2));
    }
};

export const getActualCostByDates = (actualCost, dateStart, dateEnd) => {
    dateStart = new Date(Date.UTC(dateStart.getFullYear(), dateStart.getMonth(), dateStart.getDate()));
    dateEnd = new Date(Date.UTC(dateEnd.getFullYear(), dateEnd.getMonth(), dateEnd.getDate()));
    let totalCost = 0;

    if (Array.isArray(actualCost)) {
        for (const entry of actualCost) {
            const { year, month, cost } = entry;
            const daysInMonth = new Date(year, month, 0).getDate();
            const perDayCost = cost / daysInMonth;

            const monthStart = new Date(Date.UTC(year, month - 1, 1));
            const monthEnd = new Date(Date.UTC(year, month - 1, daysInMonth));
            const overlapStart = dateStart > monthStart ? dateStart : monthStart;
            const overlapEnd = dateEnd < monthEnd ? dateEnd : monthEnd;

            if (overlapStart <= overlapEnd) {
                const daysOverlap = Math.floor((overlapEnd - overlapStart) / (1000 * 60 * 60 * 24)) + 1;
                totalCost += perDayCost * daysOverlap;
            }
        }
    }

    return Number(totalCost.toFixed(2));
};
